/* eslint-disable max-lines */
import {Flex, Divider, Typography, Tooltip} from 'antd';
import {memo, MouseEvent, useMemo, ReactNode} from 'react';
import {Button} from '@panda-design/components';
import {MCPServerBase} from '@/types/mcp/mcp';
import {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';
import MCPServerAvatar from '@/components/MCP/MCPServerAvatar';
import MCPCard from '@/design/MCP/MCPCard';
import {getServerTypeText} from '@/components/MCP/MCPServerTypeTag';
import TagGroup from '@/components/MCP/TagGroup';
import {MCPCollectButton} from '@/components/MCP/MCPCollectButton';
import SvgEye from '@/icons/mcp/Eye';
import SvgCallCount from '@/icons/mcp/CallCount';
import SvgPremium from '@/icons/mcp/Vip';
import {
    actionButtonStyle,
    cardContentStyle,
    containerCss,
    DescriptionContainer,
    DescriptionText,
    dividerStyle,
    EllipsisOverlay,
    formatCount,
    fullWidthButtonStyle,
    hoverActionsStyle,
    iconStyle,
    protocolTextStyle,
    statsContainerStyle,
} from './BaseMCPCard.styles';

interface Props {
    server: MCPServerBase;
    refresh: () => void;
    showDepartment?: boolean;
    workspaceId?: number;
    onCardClick: () => void;
    onViewCountClick: (e: MouseEvent) => void;
    onPlaygroundClick?: (e: MouseEvent) => void;
    renderActions?: () => ReactNode;
    infoType?: 'publish' | 'update';
}

const BaseMCPCard = ({
    server,
    refresh,
    workspaceId,
    onCardClick,
    onViewCountClick,
    onPlaygroundClick,
    renderActions,
}: Props) => {
    const tags = useMemo(
        () => (server.labels ?? []).map((label, index) => ({
            id: label.id || index,
            label: label.labelValue,
        })),
        [server.labels]
    );

    return (
        <MCPCard vertical onClick={onCardClick} className={containerCss}>
            <Flex gap={14} align="center">
                <div style={{position: 'relative', display: 'inline-block'}}>
                    <MCPServerAvatar
                        icon={server.icon}
                        style={{
                            border: '2px solid',
                            borderImageSource:
                                'linear-gradient(237.19deg, #0183FF -52.14%, rgba(173, 215, 255, 0.6) 111.4%)',
                            borderImageSlice: 1,
                        }}
                    />
                    <SvgPremium
                        style={{
                            position: 'absolute',
                            bottom: -7,
                            right: -4,
                            fontSize: '23px',
                        }}
                    />
                </div>
                <Flex vertical justify="space-between" style={cardContentStyle} gap={4}>
                    <Typography.Title level={4} ellipsis>
                        {server.name}
                    </Typography.Title>
                    <Flex align="center" gap={4}>
                        <Typography.Text style={protocolTextStyle}>
                            {getServerTypeText(server.serverSourceType)}
                        </Typography.Text>
                        <Divider type="vertical" style={{borderColor: '#D9D9D9'}} />
                        <Typography.Text style={protocolTextStyle}>
                            {server.serverProtocolType}
                        </Typography.Text>

                    </Flex>
                </Flex>
            </Flex>
            <Tooltip title={server.description || '暂无描述'} placement="top">
                <DescriptionContainer>
                    <DescriptionText>{server.description || '暂无描述'}</DescriptionText>
                    <EllipsisOverlay />
                </DescriptionContainer>
            </Tooltip>
            <TagGroup
                labels={tags}
                color="light-purple"
                prefix={null}
                style={{flexShrink: 1, overflow: 'hidden'}}
                gap={4}
            />
            <Divider style={dividerStyle} />
            <Flex justify="space-between" align="center">
                <Flex align="center" gap={12}>
                    <Tooltip title="浏览量">
                        <Flex
                            align="center"
                            gap={4}
                            onClick={onViewCountClick}
                            className={statsContainerStyle}
                        >
                            <SvgEye style={iconStyle} />
                            {formatCount(server.serverMetrics?.viewCount || 0)}
                        </Flex>
                    </Tooltip>
                    <Tooltip title="调用量">
                        <Flex
                            align="center"
                            gap={4}
                            className={statsContainerStyle}
                        >
                            <SvgCallCount style={iconStyle} />
                            {formatCount(server.serverMetrics?.callCount || 0)}
                        </Flex>
                    </Tooltip>
                </Flex>
                <Flex align="center">
                    <MCPCollectButton
                        refresh={refresh}
                        favorite={server.favorite}
                        serverId={server.id}
                        showText
                        style={actionButtonStyle}
                    />
                    <Divider type="vertical" style={{borderColor: '#D9D9D9'}} />
                    <MCPSubscribeButton
                        refresh={refresh}
                        workspaceId={workspaceId || server.workspaceId}
                        id={server.id}
                        showText
                        style={actionButtonStyle}
                    />
                </Flex>
            </Flex>
            <Flex align="center" justify="space-between" gap={10} className={`hover-actions ${hoverActionsStyle}`}>
                {renderActions ? renderActions() : (
                    <Button type="primary" onClick={onPlaygroundClick} style={fullWidthButtonStyle}>
                        去MCP Playground使用
                    </Button>
                )}
            </Flex>
        </MCPCard>
    );
};

export default memo(BaseMCPCard);
